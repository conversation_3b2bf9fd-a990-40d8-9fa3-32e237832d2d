#!/usr/bin/env python3

import asyncio
import json
import logging
import os
import pprint
import ssl
import certifi
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urljoin, urlencode
import aiohttp
from mcp.server.models import InitializationOptions
import mcp.types as types
from mcp.server import NotificationOptions, Server
import mcp.server.stdio
from dotenv import load_dotenv


load_dotenv()
os.environ["REQUESTS_CA_BUNDLE"] = certifi.where()
# Configure logging
logging.basicConfig(level=logging.INFO, filename="mcp-ckan-server.log")
logger = logging.getLogger("mcp-ckan-server")

class CKANAPIClient:
    """CKAN API client for making HTTP requests"""
    
    def __init__(self, base_url: str, api_key: Optional[str] = None):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.session = None
    
    async def __aenter__(self):
        ssl_context = ssl.create_default_context(cafile=certifi.where())
        self.session = aiohttp.ClientSession(
            connector=aiohttp.TCPConnector(ssl=ssl_context),
            timeout=aiohttp.ClientTimeout(total=30)
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def _get_headers(self) -> Dict[str, str]:
        headers = {
            'User-Agent': 'MCP-CKAN-Server/1.0',
            'Accept': 'application/json, text/plain, */*'
        }
        if self.api_key:
            headers['Authorization'] = self.api_key
        return headers
    
    async def _make_request(self, method: str, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make HTTP request to CKAN API"""
        url = urljoin(f"{self.base_url}/api/3/action/", endpoint)
        headers = self._get_headers()
        
        try:
            # Use params for GET requests instead of building query string manually
            async with self.session.request(method, url, headers=headers, params=params) as response:
                logger.info(f"Request URL: {response.url}")
                logger.info(f"Response status: {response.status}")
                logger.info(f"Response headers: {dict(response.headers)}")
                
                text = await response.text()
                logger.info(f"Raw response text (first 500 chars): {text[:500]}")

                if response.status != 200:
                    raise Exception(f"HTTP {response.status}: {text}")
                
                # Try to parse as JSON, regardless of content-type
                try:
                    result = json.loads(text)
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse JSON: {e}")
                    logger.error(f"Response text: {text}")
                    raise Exception(f"Invalid JSON response: {text[:200]}...")
                
                if not result.get('success', False):
                    error_msg = result.get('error', {})
                    raise Exception(f"CKAN API Error: {error_msg}")
                 
                return result.get('result', {})
                
        except aiohttp.ClientError as e:
            logger.error(f"HTTP Client Error: {str(e)}")
            raise Exception(f"HTTP Error: {str(e)}")
        except Exception as e:
            logger.error(f"Request failed: {str(e)}")
            raise Exception(f"Request failed: {str(e)}")

# Global CKAN client
ckan_client = None

# Initialize MCP server
server = Server("ckan-mcp-server")

@server.list_tools()
async def handle_list_tools() -> List[types.Tool]:
    """List available CKAN API tools"""
    return [
        types.Tool(
            name="ckan_package_list",
            description="Get list of all packages (datasets) in CKAN (unsorted)",
            inputSchema={
                "type": "object",
                "properties": {
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of packages to return",
                        "default": 100
                    },
                    "offset": {
                        "type": "integer", 
                        "description": "Offset for pagination",
                        "default": 0
                    }
                }
            }
        ),
        types.Tool(
            name="ckan_package_show",
            description="Get details of a specific package/dataset (like dates)",
            inputSchema={
                "type": "object",
                "properties": {
                    "id": {
                        "type": "string",
                        "description": "Package ID or name"
                    }
                },
                "required": ["id"]
            }
        ),
        types.Tool(
            name="ckan_package_search",
            description="Search for packages using queries",
            inputSchema={
                "type": "object",
                "properties": {
                    "q": {
                        "type": "string",
                        "description": "Search query",
                        "default": "*:*"
                    },
                    "fq": {
                        "type": "string",
                        "description": "Filter query"
                    },
                    "sort": {
                        "type": "string",
                        "description": "Sort field and direction (e.g., 'score desc')"
                    },
                    "rows": {
                        "type": "integer",
                        "description": "Number of results to return",
                        "default": 10
                    },
                    "start": {
                        "type": "integer",
                        "description": "Offset for pagination",
                        "default": 0
                    }
                }
            }
        ),
        types.Tool(
            name="ckan_organization_list",
            description="Get list of all organizations",
            inputSchema={
                "type": "object",
                "properties": {
                    "all_fields": {
                        "type": "boolean",
                        "description": "Include all organization fields",
                        "default": False
                    }
                }
            }
        ),
        types.Tool(
            name="ckan_organization_show",
            description="Get details of a specific organization",
            inputSchema={
                "type": "object",
                "properties": {
                    "id": {
                        "type": "string",
                        "description": "Organization ID or name"
                    },
                    "include_datasets": {
                        "type": "boolean",
                        "description": "Include organization's datasets",
                        "default": False
                    }
                },
                "required": ["id"]
            }
        ),
        types.Tool(
            name="ckan_group_list",
            description="Get list of all groups",
            inputSchema={
                "type": "object",
                "properties": {
                    "all_fields": {
                        "type": "boolean",
                        "description": "Include all group fields",
                        "default": False
                    }
                }
            }
        ),
        types.Tool(
            name="ckan_tag_list",
            description="Get list of all tags",
            inputSchema={
                "type": "object",
                "properties": {
                    "vocabulary_id": {
                        "type": "string",
                        "description": "Vocabulary ID to filter tags"
                    }
                }
            }
        ),
        types.Tool(
            name="ckan_resource_show",
            description="Get details of a specific resource",
            inputSchema={
                "type": "object",
                "properties": {
                    "id": {
                        "type": "string",
                        "description": "Resource ID"
                    }
                },
                "required": ["id"]
            }
        ),
        types.Tool(
            name="ckan_site_read",
            description="Get site information and statistics",
            inputSchema={
                "type": "object",
                "properties": {}
            }
        ),
        types.Tool(
            name="ckan_status_show",
            description="Get CKAN site status and version information",
            inputSchema={
                "type": "object",
                "properties": {}
            }
        ),
        types.Tool(
            name="ckan_package_list_detailed",
            description="Get detailed list of packages with full information (use sparingly)",
            inputSchema={
                "type": "object",
                "properties": {
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of packages to return (max 10)",
                        "default": 5
                    },
                    "offset": {
                        "type": "integer",
                        "description": "Offset for pagination",
                        "default": 0
                    }
                }
            }
        )
    ]

server_info = {
    "name": "Berlin Open Data MCP Server",
    "description": "Provides access to Berlin's open data portal and datasets",
    "region": "Berlin, Germany",
    "data_source": "https://datenregister.berlin.de/"
}

def _truncate_text(text: str, max_length: int = 200) -> str:
    """Truncate text to maximum length"""
    if len(text) <= max_length:
        return text
    return text[:max_length] + "..."

def _summarize_result(result: Any, tool_name: str) -> Dict[str, Any]:
    """Summarize API results to make them more concise"""
    if tool_name == "ckan_package_list":
        if isinstance(result, list):
            return {
                "total_packages": len(result),
                "packages": result[:10],  # Only first 10
                "note": f"Showing first 10 of {len(result)} packages"
            }
    
    elif tool_name == "ckan_package_show":
        if isinstance(result, dict):
            # Keep only essential fields
            summary = {
                "id": result.get("id"),
                "name": result.get("name"),
                "title": result.get("title"),
                "notes": _truncate_text(result.get("notes", ""), 300),
                "metadata_created": result.get("metadata_created"),
                "metadata_modified": result.get("metadata_modified"),
                "organization": result.get("organization", {}).get("name") if result.get("organization") else None,
                "tags": [tag.get("name") for tag in result.get("tags", [])[:10]],
                "groups": [group.get("name") for group in result.get("groups", [])],
                "resources": [
                    {
                        "id": res.get("id"),
                        "name": res.get("name"),
                        "format": res.get("format"),
                        "url": res.get("url"),
                        "size": res.get("size")
                    } for res in result.get("resources", [])[:5]
                ],
                "extras": {extra.get("key"): extra.get("value") for extra in result.get("extras", [])[:10]}
            }
            if len(result.get("resources", [])) > 5:
                summary["note"] = f"Showing first 5 of {len(result.get('resources', []))} resources"
            return summary
    
    elif tool_name == "ckan_package_search":
        if isinstance(result, dict):
            packages = result.get("results", [])
            return {
                "count": result.get("count", 0),
                "total_results": len(packages),
                "results": [
                    {
                        "id": pkg.get("id"),
                        "name": pkg.get("name"),
                        "title": pkg.get("title"),
                        "notes": _truncate_text(pkg.get("notes", ""), 150),
                        "organization": pkg.get("organization", {}).get("name") if pkg.get("organization") else None,
                        "tags": [tag.get("name") for tag in pkg.get("tags", [])[:5]],
                        "metadata_modified": pkg.get("metadata_modified"),
                        "resources_count": len(pkg.get("resources", []))
                    } for pkg in packages[:10]
                ],
                "facets": result.get("facets", {}),
                "note": f"Showing first 10 results" if len(packages) > 10 else None
            }
    
    elif tool_name == "ckan_organization_list":
        if isinstance(result, list):
            return {
                "total_organizations": len(result),
                "organizations": result[:20],  # First 20
                "note": f"Showing first 20 of {len(result)} organizations" if len(result) > 20 else None
            }
    
    elif tool_name == "ckan_organization_show":
        if isinstance(result, dict):
            return {
                "id": result.get("id"),
                "name": result.get("name"),
                "title": result.get("title"),
                "description": _truncate_text(result.get("description", ""), 300),
                "created": result.get("created"),
                "package_count": result.get("package_count"),
                "packages": [pkg.get("name") for pkg in result.get("packages", [])[:10]],
                "extras": {extra.get("key"): extra.get("value") for extra in result.get("extras", [])[:10]},
                "note": f"Showing first 10 packages" if len(result.get("packages", [])) > 10 else None
            }
    
    elif tool_name == "ckan_group_list":
        if isinstance(result, list):
            return {
                "total_groups": len(result),
                "groups": result[:20],
                "note": f"Showing first 20 of {len(result)} groups" if len(result) > 20 else None
            }
    
    elif tool_name == "ckan_tag_list":
        if isinstance(result, list):
            return {
                "total_tags": len(result),
                "tags": result[:50],  # First 50 tags
                "note": f"Showing first 50 of {len(result)} tags" if len(result) > 50 else None
            }
    
    elif tool_name == "ckan_resource_show":
        if isinstance(result, dict):
            return {
                "id": result.get("id"),
                "name": result.get("name"),
                "description": _truncate_text(result.get("description", ""), 300),
                "format": result.get("format"),
                "url": result.get("url"),
                "size": result.get("size"),
                "created": result.get("created"),
                "last_modified": result.get("last_modified"),
                "mimetype": result.get("mimetype"),
                "package_id": result.get("package_id")
            }
    
    # For other tools, return as-is but with some limits
    return result

@server.call_tool()
async def handle_call_tool(name: str, arguments: Optional[Dict[str, Any]]) -> List[types.TextContent]:
    """Handle tool calls to CKAN API"""
    if not ckan_client:
        raise Exception("CKAN client not initialized. Please set CKAN_URL environment variable.")
    
    try:
        if name == "ckan_package_list":
            params = {}
            if arguments:
                if "limit" in arguments:
                    params["limit"] = min(arguments["limit"], 50)  # Limit to max 50
                if "offset" in arguments:
                    params["offset"] = arguments["offset"]
            result = await ckan_client._make_request("GET", "package_list", params)
            
        elif name == "ckan_package_show":
            params = {"id": arguments["id"]}
            result = await ckan_client._make_request("GET", "package_show", params)
            
        elif name == "ckan_package_search":
            params = arguments or {}
            # Remove None values and limit rows
            params = {k: v for k, v in params.items() if v is not None}
            if "rows" in params:
                params["rows"] = min(params["rows"], 20)  # Limit to max 20
            result = await ckan_client._make_request("GET", "package_search", params)
            
        elif name == "ckan_organization_list":
            params = {}
            if arguments and "all_fields" in arguments:
                params["all_fields"] = arguments["all_fields"]
            result = await ckan_client._make_request("GET", "organization_list", params)
            
        elif name == "ckan_organization_show":
            params = {"id": arguments["id"]}
            if arguments.get("include_datasets"):
                params["include_datasets"] = arguments["include_datasets"]
            result = await ckan_client._make_request("GET", "organization_show", params)
            
        elif name == "ckan_group_list":
            params = {}
            if arguments and "all_fields" in arguments:
                params["all_fields"] = arguments["all_fields"]
            result = await ckan_client._make_request("GET", "group_list", params)
            
        elif name == "ckan_tag_list":
            params = arguments or {}
            params = {k: v for k, v in params.items() if v is not None}
            result = await ckan_client._make_request("GET", "tag_list", params)
            
        elif name == "ckan_resource_show":
            params = {"id": arguments["id"]}
            result = await ckan_client._make_request("GET", "resource_show", params)
            
        elif name == "ckan_site_read":
            result = await ckan_client._make_request("GET", "site_read")
            
        elif name == "ckan_status_show":
            result = await ckan_client._make_request("GET", "status_show")
            
        elif name == "ckan_package_list_detailed":
            params = {}
            if arguments:
                if "limit" in arguments:
                    params["limit"] = min(arguments["limit"], 10)  # Max 10 for detailed
                if "offset" in arguments:
                    params["offset"] = arguments["offset"]
            # Get package list first
            package_list = await ckan_client._make_request("GET", "package_list", params)
            
            # Get detailed info for each package
            detailed_packages = []
            for package_id in package_list[:5]:  # Limit to 5 detailed packages
                try:
                    package_detail = await ckan_client._make_request("GET", "package_show", {"id": package_id})
                    detailed_packages.append(_summarize_result(package_detail, "ckan_package_show"))
                except Exception as e:
                    logger.warning(f"Could not get details for package {package_id}: {e}")
                    detailed_packages.append({"id": package_id, "error": str(e)})
            
            result = {
                "total_in_list": len(package_list),
                "detailed_packages": detailed_packages,
                "note": "Showing detailed info for first 5 packages only"
            }
            
        else:
            raise Exception(f"Unknown tool: {name}")
        
        # Summarize the result to make it more concise
        summarized_result = _summarize_result(result, name)
        
        # Convert to JSON and check length
        json_text = json.dumps(summarized_result, indent=2, ensure_ascii=False)
        
        # If still too long, truncate further
        if len(json_text) > 8000:  # Reasonable limit for Claude
            json_text = json_text[:8000] + "\n... (truncated)"
        
        return [
            types.TextContent(
                type="text",
                text=json_text
            )
        ]
        
    except Exception as e:
        logger.error(f"Error calling tool {name}: {str(e)}")
        return [
            types.TextContent(
                type="text", 
                text=f"Error: {str(e)}"
            )
        ]

@server.list_resources()
async def handle_list_resources() -> List[types.Resource]:
    """List available CKAN resources"""
    return [
        types.Resource(
            uri="ckan://api/docs",
            name="CKAN API Documentation",
            description="Official CKAN API documentation and endpoints",
            mimeType="text/plain"
        ),
        types.Resource(
            uri="ckan://config",
            name="CKAN Server Configuration",
            description="Current CKAN server configuration and connection details",
            mimeType="application/json"
        )
    ]

@server.read_resource()
async def handle_read_resource(uri: str) -> str:
    """Read CKAN resources"""
    if uri == "ckan://api/docs":
        return """
CKAN API Documentation Summary

Base URL: Configure via CKAN_URL environment variable
API Version: 3

Key Endpoints:
- package_list: Get all packages/datasets
- package_show: Get package details
- package_search: Search packages
- organization_list: Get all organizations  
- organization_show: Get organization details
- group_list: Get all groups
- tag_list: Get all tags
- resource_show: Get resource details
- site_read: Get site information
- status_show: Get site status

Authentication: Set CKAN_API_KEY environment variable for write operations

Full documentation: https://docs.ckan.org/en/latest/api/
        """
    elif uri == "ckan://config":
        config = {
            "base_url": ckan_client.base_url if ckan_client else "Not configured",
            "api_key_configured": bool(ckan_client and ckan_client.api_key),
            "session_active": bool(ckan_client and ckan_client.session)
        }
        return json.dumps(config, indent=2)
    else:
        raise Exception(f"Unknown resource: {uri}")

async def main():
    """Main server function"""
    import os
    
    # Initialize CKAN client
    ckan_url = os.getenv("CKAN_URL")
    if not ckan_url:
        logger.error("CKAN_URL environment variable not set")
        raise Exception("CKAN_URL environment variable is required")
    
    ckan_api_key = os.getenv("CKAN_API_KEY")
    
    global ckan_client
    ckan_client = CKANAPIClient(ckan_url, ckan_api_key)
    
    # Start the CKAN client session
    await ckan_client.__aenter__()
    
    try:
        # Run the MCP server
        async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
            await server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="ckan-mcp-server",
                    server_version="1.0.0",
                    capabilities=server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )
    finally:
        # Clean up CKAN client
        await ckan_client.__aexit__(None, None, None)

if __name__ == "__main__":
    asyncio.run(main())