#!/usr/bin/env python3

"""
Test script to verify text/plain response handling in the Parla MCP server
"""

import asyncio
import json
from unittest.mock import AsyncMock, MagicMock

# Mock the dependencies that might not be available
class MockResponse:
    def __init__(self, text_content, status=200, content_type="text/plain"):
        self.status = status
        self.headers = {"Content-Type": content_type}
        self._text = text_content
    
    async def text(self):
        return self._text

class MockParlaAPIClient:
    """Mock version of ParlaAPIClient for testing"""
    
    def __init__(self):
        self.session = None
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass
    
    async def _handle_response(self, response, url: str):
        """Handle API response, supporting both JSON and plain text"""
        print(f"Request URL: {url}")
        print(f"Response status: {response.status}")
        
        text = await response.text()
        print(f"Raw response text (first 100 chars): {text[:100]}")

        if response.status not in [200, 201]:
            raise Exception(f"HTTP {response.status}: {text}")

        content_type = response.headers.get("Content-Type", "").lower()
        print(f"Response Content-Type: {content_type}")

        if "application/json" in content_type:
            try:
                json_data = json.loads(text)
                print("Successfully parsed JSON response")
                return json_data
            except json.JSONDecodeError as e:
                print(f"Failed to parse JSON: {e}")
                raise Exception(f"Invalid JSON response: {text[:200]}...")
        
        elif "text/plain" in content_type:
            # Wrap plain text response in a dict to keep interface consistent
            print("Handling text/plain response")
            return {"text": text.strip()}
        
        else:
            # Default to treating as text if content type is unknown or unexpected
            print(f"Unexpected content type: {content_type}, treating as text")
            return {"text": text.strip()}

def _format_response(result, tool_name: str):
    """Format API responses to be more readable"""
    # Handle text/plain responses
    if "text" in result and len(result) == 1:
        # This is a text/plain response wrapped in a dict
        return {
            "response": result["text"],
            "format": "text/plain",
            "tool": tool_name
        }
    
    if tool_name == "parla_generate_answer":
        # Handle both JSON and text responses
        if "text" in result:
            return {
                "answer": result["text"],
                "format": "text/plain",
                "tool": tool_name
            }
        
        # Handle JSON response (existing logic)
        formatted = {
            "answer": result.get("answer", "No answer provided"),
            "sources": result.get("sources", []),
            "request_id": result.get("userRequestId"),
            "query": result.get("query")
        }
        return formatted
    
    return result

async def test_text_plain_response():
    """Test handling of text/plain responses"""
    print("=== Testing text/plain response handling ===")
    
    client = MockParlaAPIClient()
    
    # Test 1: text/plain response
    plain_text_response = MockResponse(
        "This is a plain text answer about Berlin Parliament housing policy. The recent decisions include...",
        content_type="text/plain"
    )
    
    result = await client._handle_response(plain_text_response, "https://api.parla.berlin/generate-answer")
    print(f"Raw result: {result}")
    
    formatted = _format_response(result, "parla_generate_answer")
    print(f"Formatted result: {formatted}")
    
    # Test 2: JSON response
    json_response = MockResponse(
        '{"answer": "JSON answer about housing policy", "sources": [], "userRequestId": "123"}',
        content_type="application/json"
    )
    
    result2 = await client._handle_response(json_response, "https://api.parla.berlin/generate-answer")
    print(f"Raw JSON result: {result2}")
    
    formatted2 = _format_response(result2, "parla_generate_answer")
    print(f"Formatted JSON result: {formatted2}")
    
    print("\n=== Test completed successfully ===")

if __name__ == "__main__":
    asyncio.run(test_text_plain_response())
