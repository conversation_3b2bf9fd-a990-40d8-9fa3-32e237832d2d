#!/usr/bin/env python3

"""
Test script to verify the Parla MCP server works with text/plain responses
"""

import asyncio
import sys
import os

# Add the current directory to the path so we can import the server
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from parla_mcp_server import ParlaAPIClient
    print("Successfully imported ParlaAPIClient")
except ImportError as e:
    print(f"Could not import ParlaAPIClient: {e}")
    print("This is expected if the MCP dependencies are not installed.")
    print("The server should still work when run in the proper environment.")
    sys.exit(0)

async def test_parla_api():
    """Test the Parla API client with text/plain handling"""
    print("=== Testing Parla API Client ===")
    
    async with ParlaAPIClient() as client:
        try:
            # Test a simple query that might return text/plain
            print("Testing generate_answer with a simple query...")
            result = await client.generate_answer("Was ist das CityLAB Berlin?")
            print(f"Result type: {type(result)}")
            print(f"Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
            
            if isinstance(result, dict):
                if "text" in result:
                    print("SUCCESS: Received text/plain response")
                    print(f"Response text (first 200 chars): {result['text'][:200]}...")
                elif "answer" in result:
                    print("SUCCESS: Received JSON response")
                    print(f"Answer (first 200 chars): {result['answer'][:200]}...")
                else:
                    print(f"Received response with keys: {list(result.keys())}")
            
        except Exception as e:
            print(f"Error during API test: {e}")
            print("This might be expected if the API is not accessible or returns unexpected format")

if __name__ == "__main__":
    asyncio.run(test_parla_api())
