#!/usr/bin/env python3

import asyncio
import json
import logging
import ssl
import certifi
import uuid
from typing import Any, Dict, List, Optional
import aiohttp
from mcp.server.models import InitializationOptions
import mcp.types as types
from mcp.server import NotificationOptions, Server
import mcp.server.stdio

# Configure logging
logging.basicConfig(level=logging.INFO, filename="parla_mcp_server.log")
logger = logging.getLogger("parla_mcp_server")

class ParlaAPIClient:
    """Parla API client for making HTTP requests to the Berlin Parliament API"""
    
    def __init__(self, base_url: str = "https://api.parla.berlin"):
        self.base_url = base_url.rstrip('/')
        self.session = None
    
    async def __aenter__(self):
        ssl_context = ssl.create_default_context(cafile=certifi.where())
        self.session = aiohttp.ClientSession(
            connector=aiohttp.TCPConnector(ssl=ssl_context),
            timeout=aiohttp.ClientTimeout(total=60)  # Longer timeout for AI responses
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    def _get_headers(self) -> Dict[str, str]:
        return {
            'Content-Type': 'application/json',
            'User-Agent': 'MCP-Parla-Server/1.0',
            'Accept': 'text/plain, application/json'
        }
    
    async def generate_answer(self, query: str, include_summary: bool = True, 
                            document_matches: Optional[List] = None, 
                            user_request_id: Optional[str] = None) -> Dict[str, Any]:
        """Generate an answer using the Parla API"""
        if user_request_id is None:
            user_request_id = str(uuid.uuid4())
        
        if document_matches is None:
            document_matches = []
        
        payload = {
            "query": query,
            "include_summary_in_response_generation": include_summary,
            "documentMatches": document_matches,
            "userRequestId": user_request_id
        }
        
        return await self._make_request("POST", "/generate-answer", payload)
    
    async def search_documents(self, query: str, limit: int = 10, 
                             filters: Optional[Dict] = None) -> Dict[str, Any]:
        """Search for documents in the Parla database"""
        payload = {
            "query": query,
            "limit": limit
        }
        if filters:
            payload.update(filters)
        
        return await self._make_request("POST", "/search", payload)
    
    async def get_document_details(self, document_id: str) -> Dict[str, Any]:
        """Get details for a specific document"""
        return await self._make_request("GET", f"/documents/{document_id}")
    
    async def _make_request(self, method: str, endpoint: str, 
                          payload: Optional[Dict] = None) -> Dict[str, Any]:
        """Make HTTP request to Parla API"""
        url = f"{self.base_url}{endpoint}"
        headers = self._get_headers()
        
        try:
            if method == "POST":
                async with self.session.post(url, headers=headers, json=payload) as response:
                    return await self._handle_response(response, url)
            elif method == "GET":
                async with self.session.get(url, headers=headers) as response:
                    return await self._handle_response(response, url)
            else:
                raise Exception(f"Unsupported HTTP method: {method}")
                
        except aiohttp.ClientError as e:
            logger.error(f"HTTP Client Error for {url}: {str(e)}")
            raise Exception(f"HTTP Error: {str(e)}")
        except Exception as e:
            logger.error(f"Request failed for {url}: {str(e)}")
            raise Exception(f"Request failed: {str(e)}")

    async def _handle_response(self, response, url: str) -> Dict[str, Any]:
        """Handle API response, supporting both JSON and plain text"""
        logger.info(f"Request URL: {url}")
        logger.info(f"Response status: {response.status}")

        text = await response.text()
        logger.info(f"Raw response text (first 500 chars): {text[:500]}")

        if response.status not in [200, 201]:
            raise Exception(f"HTTP {response.status}: {text}")

        content_type = response.headers.get("Content-Type", "").lower()
        logger.info(f"Response Content-Type: {content_type}")

        # First, try to determine if this looks like JSON regardless of content-type
        text_stripped = text.strip()
        looks_like_json = (text_stripped.startswith('{') and text_stripped.endswith('}')) or \
                         (text_stripped.startswith('[') and text_stripped.endswith(']'))

        # Try JSON parsing if content-type suggests JSON OR if it looks like JSON
        if "application/json" in content_type or looks_like_json:
            try:
                json_data = json.loads(text)
                logger.info("Successfully parsed JSON response")
                return json_data
            except json.JSONDecodeError as e:
                logger.info(f"Failed to parse as JSON (treating as text): {e}")
                # Fall through to text handling instead of raising an error

        # Handle as plain text (either explicitly marked as text/plain or JSON parsing failed)
        logger.info("Handling response as plain text")
        return {"text": text.strip()}

# Global Parla client
parla_client = None

# Initialize MCP server
server = Server("parla-mcp-server")

@server.list_tools()
async def handle_list_tools() -> List[types.Tool]:
    """List available Parla API tools"""
    return [
        types.Tool(
            name="parla_generate_answer",
            description="Generate an answer about Berlin Parliament topics using Parla AI",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Question or query about Berlin Parliament"
                    },
                    "include_summary": {
                        "type": "boolean",
                        "description": "Include summary in response generation",
                        "default": True
                    },
                    "document_matches": {
                        "type": "array",
                        "description": "Specific documents to include in the search",
                        "items": {"type": "object"},
                        "default": []
                    },
                    "user_request_id": {
                        "type": "string",
                        "description": "Optional user request ID for tracking"
                    }
                },
                "required": ["query"]
            }
        ),
        types.Tool(
            name="parla_search_documents",
            description="Search for documents in the Berlin Parliament database",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query for documents"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of documents to return",
                        "default": 10,
                        "minimum": 1,
                        "maximum": 50
                    },
                    "filters": {
                        "type": "object",
                        "description": "Additional filters for the search",
                        "properties": {
                            "document_type": {"type": "string"},
                            "date_from": {"type": "string"},
                            "date_to": {"type": "string"},
                            "committee": {"type": "string"}
                        }
                    }
                },
                "required": ["query"]
            }
        ),
        types.Tool(
            name="parla_get_document",
            description="Get details for a specific Berlin Parliament document",
            inputSchema={
                "type": "object",
                "properties": {
                    "document_id": {
                        "type": "string",
                        "description": "Document ID to retrieve"
                    }
                },
                "required": ["document_id"]
            }
        ),
        types.Tool(
            name="parla_ask_about_topic",
            description="Ask a specific question about Berlin Parliament topics with context",
            inputSchema={
                "type": "object",
                "properties": {
                    "topic": {
                        "type": "string",
                        "description": "Main topic (e.g., 'housing policy', 'budget', 'climate')"
                    },
                    "question": {
                        "type": "string",
                        "description": "Specific question about the topic"
                    },
                    "time_period": {
                        "type": "string",
                        "description": "Time period to focus on (e.g., '2024', 'last year', 'recent')",
                        "default": "recent"
                    }
                },
                "required": ["topic", "question"]
            }
        )
    ]

def _truncate_text(text: str, max_length: int = 1000) -> str:
    """Truncate text to maximum length"""
    if not text or len(text) <= max_length:
        return text
    return text[:max_length] + "... (truncated)"

def _format_response(result: Dict[str, Any], tool_name: str) -> Dict[str, Any]:
    """Format API responses to be more readable"""
    # Handle text/plain responses
    if "text" in result and len(result) == 1:
        # This is a text/plain response wrapped in a dict
        return {
            "response": result["text"],
            "format": "text/plain",
            "tool": tool_name
        }

    if tool_name == "parla_generate_answer":
        # Handle both JSON and text responses
        if "text" in result:
            return {
                "answer": result["text"],
                "format": "text/plain",
                "tool": tool_name
            }

        formatted = {
            "answer": result.get("answer", "No answer provided"),
            "sources": result.get("sources", []),
            "request_id": result.get("userRequestId"),
            "query": result.get("query")
        }

        # Truncate very long answers
        if formatted["answer"]:
            formatted["answer"] = _truncate_text(formatted["answer"], 2000)

        # Limit sources to first 5
        if len(formatted["sources"]) > 5:
            formatted["sources"] = formatted["sources"][:5]
            formatted["sources_note"] = f"Showing first 5 of {len(result.get('sources', []))} sources"

        return formatted
    
    elif tool_name == "parla_search_documents":
        # Handle text/plain response
        if "text" in result:
            return {
                "search_results": result["text"],
                "format": "text/plain",
                "tool": tool_name
            }

        if "documents" in result:
            documents = result["documents"][:10]  # Limit to 10 documents
            formatted_docs = []

            for doc in documents:
                formatted_doc = {
                    "id": doc.get("id"),
                    "title": _truncate_text(doc.get("title", ""), 200),
                    "summary": _truncate_text(doc.get("summary", ""), 300),
                    "date": doc.get("date"),
                    "document_type": doc.get("document_type"),
                    "relevance_score": doc.get("relevance_score")
                }
                formatted_docs.append(formatted_doc)

            return {
                "total_found": result.get("total", len(documents)),
                "documents": formatted_docs,
                "query": result.get("query"),
                "note": "Results limited to first 10 documents" if len(result.get("documents", [])) > 10 else None
            }
    
    elif tool_name == "parla_get_document":
        # Handle text/plain response
        if "text" in result:
            return {
                "document_content": result["text"],
                "format": "text/plain",
                "tool": tool_name
            }

        return {
            "id": result.get("id"),
            "title": result.get("title"),
            "content": _truncate_text(result.get("content", ""), 1500),
            "summary": _truncate_text(result.get("summary", ""), 500),
            "date": result.get("date"),
            "document_type": result.get("document_type"),
            "committee": result.get("committee"),
            "tags": result.get("tags", [])[:10],  # First 10 tags
            "metadata": result.get("metadata", {})
        }
    
    return result

@server.call_tool()
async def handle_call_tool(name: str, arguments: Optional[Dict[str, Any]]) -> List[types.TextContent]:
    """Handle tool calls to Parla API"""
    if not parla_client:
        raise Exception("Parla client not initialized.")
    
    try:
        if name == "parla_generate_answer":
            query = arguments["query"]
            include_summary = arguments.get("include_summary", True)
            document_matches = arguments.get("document_matches", [])
            user_request_id = arguments.get("user_request_id")
            
            result = await parla_client.generate_answer(
                query=query,
                include_summary=include_summary,
                document_matches=document_matches,
                user_request_id=user_request_id
            )
            
        elif name == "parla_search_documents":
            query = arguments["query"]
            limit = arguments.get("limit", 10)
            filters = arguments.get("filters")
            
            result = await parla_client.search_documents(
                query=query,
                limit=limit,
                filters=filters
            )
            
        elif name == "parla_get_document":
            document_id = arguments["document_id"]
            result = await parla_client.get_document_details(document_id)
            
        elif name == "parla_ask_about_topic":
            topic = arguments["topic"]
            question = arguments["question"]
            time_period = arguments.get("time_period", "recent")
            
            # Construct a more specific query
            full_query = f"Regarding {topic} in Berlin Parliament"
            if time_period != "recent":
                full_query += f" in {time_period}"
            full_query += f": {question}"
            
            result = await parla_client.generate_answer(
                query=full_query,
                include_summary=True
            )
            
        else:
            raise Exception(f"Unknown tool: {name}")
        
        # Format the result
        formatted_result = _format_response(result, name)

        # Handle text/plain responses differently
        if formatted_result.get("format") == "text/plain":
            # For text/plain responses, return the text directly with minimal formatting
            response_text = formatted_result.get("response") or \
                           formatted_result.get("answer") or \
                           formatted_result.get("search_results") or \
                           formatted_result.get("document_content") or \
                           "No content available"

            # Add a simple header to indicate the tool used
            final_text = f"[{name}]\n\n{response_text}"

            # If response is too long, truncate
            if len(final_text) > 10000:
                final_text = final_text[:10000] + "\n... (response truncated)"

            return [
                types.TextContent(
                    type="text",
                    text=final_text
                )
            ]
        else:
            # Convert JSON responses to formatted JSON
            json_text = json.dumps(formatted_result, indent=2, ensure_ascii=False)

            # If response is too long, truncate
            if len(json_text) > 10000:
                json_text = json_text[:10000] + "\n... (response truncated)"

            return [
                types.TextContent(
                    type="text",
                    text=json_text
                )
            ]
        
    except Exception as e:
        logger.error(f"Error calling tool {name}: {str(e)}")
        return [
            types.TextContent(
                type="text", 
                text=f"Error: {str(e)}"
            )
        ]

@server.list_resources()
async def handle_list_resources() -> List[types.Resource]:
    """List available Parla resources"""
    return [
        types.Resource(
            uri="parla://api/docs",
            name="Parla API Documentation",
            description="Information about the Berlin Parliament AI API",
            mimeType="text/plain"
        ),
        types.Resource(
            uri="parla://examples",
            name="Query Examples",
            description="Examples of effective queries for the Berlin Parliament",
            mimeType="text/plain"
        )
    ]

@server.read_resource()
async def handle_read_resource(uri: str) -> str:
    """Read Parla resources"""
    if uri == "parla://api/docs":
        return """
Parla Berlin Parliament API Documentation

Base URL: https://api.parla.berlin
Purpose: AI-powered access to Berlin Parliament documents and information

Key Endpoints:
- /generate-answer: Generate AI answers about Berlin Parliament topics
- /search: Search for relevant documents
- /documents/{id}: Get specific document details

The API provides access to:
- Parliamentary debates and discussions
- Committee meetings and reports
- Legislative proposals and decisions
- Parliamentary questions and answers
- Budget discussions
- Policy documents

Authentication: Currently no authentication required for public access

Example Topics:
- Housing policy in Berlin
- Climate change initiatives
- Budget allocations
- Transportation planning
- Education policies
- Healthcare system
        """
    elif uri == "parla://examples":
        return """
Effective Query Examples for Berlin Parliament:

General Questions:
- "What are the latest decisions on housing policy?"
- "What has been discussed about climate change in recent sessions?"
- "What budget allocations were made for education?"

Specific Searches:
- "Mieterschutz" (tenant protection)
- "Verkehrswende" (transportation transformation)
- "BER airport discussions"
- "Corona response measures"

Time-specific Queries:
- "What happened in the last parliamentary session?"
- "Housing policy decisions in 2024"
- "Budget discussions from last year"

Committee-specific:
- "Education committee recommendations"
- "Transportation committee meetings"
- "Budget committee decisions"

Best Practices:
- Use German terms for better results (many documents are in German)
- Be specific about time periods when relevant
- Include context for better AI responses
- Ask follow-up questions to dive deeper into topics
        """
    else:
        raise Exception(f"Unknown resource: {uri}")

async def main():
    """Main server function"""
    global parla_client
    parla_client = ParlaAPIClient()
    
    # Start the Parla client session
    await parla_client.__aenter__()
    
    try:
        # Run the MCP server
        async with mcp.server.stdio.stdio_server() as (read_stream, write_stream):
            await server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="parla-mcp-server",
                    server_version="1.0.0",
                    capabilities=server.get_capabilities(
                        notification_options=NotificationOptions(),
                        experimental_capabilities={},
                    ),
                ),
            )
    finally:
        # Clean up Parla client
        await parla_client.__aexit__(None, None, None)

if __name__ == "__main__":
    asyncio.run(main())