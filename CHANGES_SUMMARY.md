# Parla MCP Server - Text/Plain Response Handling Adaptation

## Summary of Changes

The Parla MCP server has been adapted to properly handle API responses in `text/plain` format instead of only JSON. This addresses the error where the server was trying to parse plain text responses as JSON.

## Key Changes Made

### 1. Updated Accept Header
- **File**: `parla_mcp_server.py`, line 43
- **Change**: Modified the Accept header to request both formats: `'Accept': 'text/plain, application/json'`
- **Purpose**: Tells the API that the client can handle both text/plain and JSON responses

### 2. Improved Response Handling Logic
- **File**: `parla_mcp_server.py`, lines 104-135
- **Key improvements**:
  - Added content-type detection and logging
  - Implemented graceful fallback: if JSON parsing fails, treat response as plain text
  - Added heuristic detection of JSON-like content (starts with `{` or `[`)
  - Removed the error-throwing behavior when JSON parsing fails

### 3. Enhanced Response Formatting
- **File**: `parla_mcp_server.py`, lines 248-284
- **Changes**:
  - Added detection for text/plain responses in the formatting function
  - Created consistent response structure for text responses
  - Added format indicators to help identify response type

### 4. Updated Tool Response Handling
- **File**: `parla_mcp_server.py`, lines 394-432
- **Changes**:
  - Added special handling for text/plain responses in the final output
  - Simplified text response formatting (removes JSON wrapper)
  - Added tool name headers for better context

## Error Resolution

### Before (Error Case)
```
Error: Request failed: Invalid JSON response: Das CityLAB Berlin ist ein Innovationslabor...
```

### After (Success Case)
```
[parla_generate_answer]

Das CityLAB Berlin ist ein Innovationslabor, das sich mit der Entwicklung und Erprobung neuer digitaler Lösungen für die Stadt beschäftigt...
```

## Technical Details

### Response Flow
1. **API Request**: Server sends request with `Accept: text/plain, application/json`
2. **Response Detection**: Server checks Content-Type header and response content
3. **Parsing Strategy**:
   - If Content-Type is `application/json` AND content looks like JSON → Parse as JSON
   - If JSON parsing fails OR Content-Type is `text/plain` → Handle as plain text
   - Default fallback → Handle as plain text
4. **Formatting**: Format response appropriately based on detected type
5. **Output**: Return formatted response to MCP client

### Backward Compatibility
- All existing JSON responses continue to work unchanged
- New text/plain responses are properly handled
- Mixed API behavior (some endpoints JSON, others text) is supported

## Testing

Created test scripts to verify the changes:
- `test_text_plain_handling.py`: Unit tests for response handling logic
- `test_parla_server.py`: Integration test with actual API client

## Benefits

1. **Robust Error Handling**: No more crashes when API returns text instead of JSON
2. **Format Flexibility**: Supports both JSON and text/plain responses seamlessly  
3. **Better User Experience**: Clean text output instead of error messages
4. **Future-Proof**: Can handle API changes in response format
5. **Logging**: Enhanced logging for debugging response format issues
